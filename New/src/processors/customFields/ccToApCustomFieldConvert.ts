/**
 * CliniCore to AutoPatient Custom Field Conversion Utility
 *
 * Transforms CliniCore custom field structures to AutoPatient format with
 * bidirectional compatibility, comprehensive logging, and graceful error handling.
 *
 * **Key Conversion Rules (Updated v2.0):**
 * - text → TEXT
 * - text (allowMultipleValues: true) → TEXTBOX_LIST
 * - textarea → LARGE_TEXT
 * - textarea (allowMultipleValues: true) → TEXTBOX_LIST
 * - select (allowMultipleValues: true) → MULTIPLE_OPTIONS
 * - select (allowMultipleValues: false) → SINGLE_OPTIONS
 * - boolean → RADIO (with Yes/Ja, No/Nein options)
 * - select-or-custom → SINGLE_OPTIONS
 * - number → NUMERICAL
 * - number (allowMultipleValues: true) → TEXTBOX_LIST
 * - telephone → PHONE
 * - telephone (allowMultipleValues: true) → TEXTBOX_LIST
 * - email → EMAIL
 * - email (allowMultipleValues: true) → TEXTBOX_LIST
 * - date → DATE
 * - Any unmapped CC field types → TEXT (fallback)
 *
 * **Features:**
 * - Strict TypeScript compliance (no `any` usage)
 * - Reversible transformations for data integrity
 * - Comprehensive error handling with request ID tracing
 * - Performance-optimized for bulk conversions
 * - Detailed logging for debugging and monitoring
 *
 * @since 1.0.0
 * @version 2.0.0
 */

import type { APPostCustomfieldType, GetCCCustomField } from "@type";
import { logCustomField } from "@/utils/logger";
import {
	getCcToApFallbackMapping,
	BOOLEAN_RADIO_OPTIONS,
	type CCFieldType
} from "./config/fieldTypeMappings";



/**
 * Convert CliniCore custom field to AutoPatient format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 * Supports dynamic option synchronization and field type evolution.
 *
 * @param ccField - CliniCore custom field object to convert
 * @returns AutoPatient custom field format ready for API submission
 *
 * @example
 * ```typescript
 * // Convert CC boolean field to AP radio with Yes/No options
 * const ccBooleanField: GetCCCustomField = {
 *   id: 1,
 *   name: "newsletter-wanted",
 *   label: "Newsletter erwünscht",
 *   type: "boolean",
 *   allowMultipleValues: false,
 *   allowedValues: [],
 *   // ... other properties
 * };
 *
 * const apField = ccToApCustomFieldConvert(ccBooleanField);
 * // Result: { name: "Newsletter erwünscht", dataType: "RADIO", options: ["Yes", "No"], ... }
 *
 * // Convert CC select with multiple values to AP multiple options
 * const ccSelectField: GetCCCustomField = {
 *   id: 2,
 *   name: "interests",
 *   label: "Interests",
 *   type: "select",
 *   allowMultipleValues: true,
 *   allowedValues: [
 *     { id: 1, value: "Sports", ... },
 *     { id: 2, value: "Music", ... }
 *   ],
 *   // ... other properties
 * };
 *
 * const apMultiField = ccToApCustomFieldConvert(ccSelectField);
 * // Result: { dataType: "MULTIPLE_OPTIONS", options: ["Sports", "Music"], ... }
 * ```
 */
export function ccToApCustomFieldConvert(
	ccField: GetCCCustomField,
	customName?: string,
	customFieldKey?: string,
): APPostCustomfieldType {
	const fieldType = ccField.type as CCFieldType;

	logCustomField("CC→AP conversion started", ccField.name, {
		ccFieldType: fieldType,
		allowMultiple: ccField.allowMultipleValues,
		hasAllowedValues: Boolean(ccField.allowedValues?.length),
		valueCount: ccField.allowedValues?.length || 0,
	});

	// Base field structure with common properties
	const baseField: APPostCustomfieldType = {
		name: customName || ccField.label || ccField.name,
		dataType: "TEXT", // Default fallback type
		placeholder: "",
		...(customFieldKey && { fieldKey: customFieldKey }),
	};

	// Use centralized mapping configuration with allowMultipleValues logic
	switch (fieldType) {
		case "text":
			return handleCcTextField(ccField, baseField);
		case "textarea":
			return handleCcTextareaField(ccField, baseField);
		case "number":
			return handleCcNumberField(ccField, baseField);
		case "telephone":
			return handleCcTelephoneField(ccField, baseField);
		case "email":
			return handleCcEmailField(ccField, baseField);
		case "date":
			return handleCcDateField(ccField, baseField);
		case "select":
			return handleCcSelectField(ccField, baseField);
		case "select-or-custom":
			return handleCcSelectOrCustomField(ccField, baseField);
		case "boolean":
			return handleCcBooleanField(ccField, baseField);
		default:
			return handleCcFallbackField(ccField, baseField, fieldType);
	}
}

/**
 * Handle CC text field conversion
 */
function handleCcTextField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value text should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP text(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
			// AP API requires textBoxListOptions to be populated for TEXTBOX_LIST fields
			textBoxListOptions: [
				{
					label: "Value 1",
					prefillValue: ""
				}
			],
			// AP API requires acceptedFormat for TEXTBOX_LIST fields
			acceptedFormat: ["text"]
		};
	}

	logCustomField("CC→AP text→TEXT conversion", ccField.name);
	return {
		...baseField,
		dataType: "TEXT",
	};
}

/**
 * Handle CC textarea field conversion
 */
function handleCcTextareaField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value textarea should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP textarea(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
			// AP API requires textBoxListOptions to be populated for TEXTBOX_LIST fields
			textBoxListOptions: [
				{
					label: "Value 1",
					prefillValue: ""
				}
			],
			// AP API requires acceptedFormat for TEXTBOX_LIST fields
			acceptedFormat: ["text"]
		};
	}

	logCustomField("CC→AP textarea→LARGE_TEXT conversion", ccField.name);
	return {
		...baseField,
		dataType: "LARGE_TEXT",
	};
}

/**
 * Handle CC number field conversion
 */
function handleCcNumberField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value number should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP number(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
			// AP API requires textBoxListOptions to be populated for TEXTBOX_LIST fields
			textBoxListOptions: [
				{
					label: "Value 1",
					prefillValue: ""
				}
			],
			// AP API requires acceptedFormat for TEXTBOX_LIST fields
			acceptedFormat: ["number"]
		};
	}

	logCustomField("CC→AP number→NUMERICAL conversion", ccField.name);
	return {
		...baseField,
		dataType: "NUMERICAL",
	};
}

/**
 * Handle CC telephone field conversion
 */
function handleCcTelephoneField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value telephone should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP telephone(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
			// AP API requires textBoxListOptions to be populated for TEXTBOX_LIST fields
			textBoxListOptions: [
				{
					label: "Phone Number",
					prefillValue: ""
				}
			],
			// AP API requires acceptedFormat for TEXTBOX_LIST fields
			acceptedFormat: ["phone"]
		};
	}

	logCustomField("CC→AP telephone→PHONE conversion", ccField.name);
	return {
		...baseField,
		dataType: "PHONE",
	};
}

/**
 * Handle CC email field conversion
 */
function handleCcEmailField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	// Check if multi-value email should become TEXTBOX_LIST
	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP email(multi)→TEXTBOX_LIST conversion", ccField.name, {
			allowMultiple: true,
			convertedType: "TEXTBOX_LIST"
		});
		return {
			...baseField,
			dataType: "TEXTBOX_LIST",
			// AP API requires textBoxListOptions to be populated for TEXTBOX_LIST fields
			textBoxListOptions: [
				{
					label: "Email Address",
					prefillValue: ""
				}
			],
			// AP API requires acceptedFormat for TEXTBOX_LIST fields
			acceptedFormat: ["email"]
		};
	}

	logCustomField("CC→AP email→EMAIL conversion", ccField.name);
	return {
		...baseField,
		dataType: "EMAIL",
	};
}

/**
 * Handle CC date field conversion
 */
function handleCcDateField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	logCustomField("CC→AP date→DATE conversion", ccField.name);
	return {
		...baseField,
		dataType: "DATE",
	};
}

/**
 * Handle CC boolean field conversion with Yes/Ja, No/Nein options
 */
function handleCcBooleanField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	logCustomField("CC→AP boolean→RADIO conversion", ccField.name, {
		options: BOOLEAN_RADIO_OPTIONS.MIXED,
		reversibleConversion: true,
	});

	return {
		...baseField,
		dataType: "RADIO",
		options: BOOLEAN_RADIO_OPTIONS.MIXED,
	};
}

/**
 * Handle CC select-or-custom field conversion
 */
function handleCcSelectOrCustomField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	logCustomField("CC→AP select-or-custom→SINGLE_OPTIONS conversion", ccField.name, {
		optionCount: options.length,
		customOptionsAllowed: true,
	});

	return {
		...baseField,
		dataType: "SINGLE_OPTIONS",
		options: options,
	};
}

/**
 * Handle CC select field conversion
 */
function handleCcSelectField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP select→MULTIPLE_OPTIONS conversion", ccField.name, {
			optionCount: options.length,
			allowMultiple: true,
		});

		return {
			...baseField,
			dataType: "MULTIPLE_OPTIONS",
			options: options,
		};
	}

	logCustomField("CC→AP select→SINGLE_OPTIONS conversion", ccField.name, {
		optionCount: options.length,
		allowMultiple: false,
	});

	return {
		...baseField,
		dataType: "SINGLE_OPTIONS",
		options: options,
	};
}

/**
 * Handle fallback conversion for unmapped CC field types
 */
function handleCcFallbackField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
	fieldType: CCFieldType,
): APPostCustomfieldType {
	const fallbackMapping = getCcToApFallbackMapping(fieldType);

	logCustomField("CC→AP fallback conversion", ccField.name, {
		ccFieldType: fieldType,
		fallbackApType: fallbackMapping.targetType,
		allowMultiple: ccField.allowMultipleValues,
		mappingNote: fallbackMapping.notes,
	});

	return {
		...baseField,
		dataType: fallbackMapping.targetType as "TEXT",
	};
}

/**
 * Extract option values from CC allowedValues array
 */
function extractOptionsFromAllowedValues(
	allowedValues: GetCCCustomField["allowedValues"],
): string[] {
	if (!allowedValues || !Array.isArray(allowedValues)) {
		return [];
	}

	return allowedValues
		.map((item) => item.value)
		.filter(
			(value): value is string => typeof value === "string" && value.length > 0,
		);
}

export default ccToApCustomFieldConvert;
