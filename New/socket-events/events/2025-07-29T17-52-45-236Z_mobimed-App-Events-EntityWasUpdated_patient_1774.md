# mobimed:App\Events\EntityWasUpdated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasUpdated
- **Model**: patient
- **Event ID**: 1774
- **Timestamp**: 2025-07-29T17:52:45.236Z
- **Webhook Event**: EntityWasUpdated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1774,
    "createdAt": "2025-07-29T17:07:15.000Z",
    "updatedAt": "2025-07-29T17:07:15.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Dolorem aut nostrud",
    "lastName": "Ad in qui elit nost",
    "dob": "1988-06-19T00:00:00.000Z",
    "ssn": "Ullamco laboris amet",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1760-126594",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": "BVAEB-OEB",
    "gender": "männlich",
    "addresses": [
      {
        "id": 1772,
        "label": null,
        "name": null,
        "street": "Aut quas et ut repud",
        "streetNumber": "Sint incidunt molli",
        "postalCode": "Culpa",
        "city": "Aliquam molestias in",
        "country": "GR",
        "primary": 1
      }
    ],
    "categories": [
      13
    ],
    "customFields": [
      11547,
      11587,
      11575,
      11582,
      11590,
      11548,
      11549,
      11550,
      11581,
      11573,
      11570,
      11551,
      11552,
      11553,
      11554,
      11555,
      11577,
      11556,
      11557,
      11576,
      11578,
      11584,
      11574,
      11571,
      11566,
      11558,
      11563,
      11559,
      11572,
      11560,
      11561,
      11579,
      11562,
      11564,
      11565,
      11589,
      11586,
      11583,
      11567,
      11588,
      11580,
      11568,
      11569,
      11585
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1774.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasUpdated",
  "model": "Patient",
  "id": 1774,
  "payload": {
    "id": 1774,
    "createdAt": "2025-07-29T17:07:15.000Z",
    "updatedAt": "2025-07-29T17:07:15.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Dolorem aut nostrud",
    "lastName": "Ad in qui elit nost",
    "dob": "1988-06-19T00:00:00.000Z",
    "ssn": "Ullamco laboris amet",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1760-126594",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": "BVAEB-OEB",
    "gender": "männlich",
    "addresses": [
      {
        "id": 1772,
        "label": null,
        "name": null,
        "street": "Aut quas et ut repud",
        "streetNumber": "Sint incidunt molli",
        "postalCode": "Culpa",
        "city": "Aliquam molestias in",
        "country": "GR",
        "primary": 1
      }
    ],
    "categories": [
      13
    ],
    "customFields": [
      11547,
      11587,
      11575,
      11582,
      11590,
      11548,
      11549,
      11550,
      11581,
      11573,
      11570,
      11551,
      11552,
      11553,
      11554,
      11555,
      11577,
      11556,
      11557,
      11576,
      11578,
      11584,
      11574,
      11571,
      11566,
      11558,
      11563,
      11559,
      11572,
      11560,
      11561,
      11579,
      11562,
      11564,
      11565,
      11589,
      11586,
      11583,
      11567,
      11588,
      11580,
      11568,
      11569,
      11585
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1774.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-29T17:52:45.236Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasUpdated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
