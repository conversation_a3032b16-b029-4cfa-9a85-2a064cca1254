# mobimed:App\Events\EntityWasCreated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasCreated
- **Model**: patient
- **Event ID**: 1774
- **Timestamp**: 2025-07-29T17:07:16.499Z
- **Webhook Event**: EntityWasCreated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1774,
    "createdAt": "2025-07-29T17:07:15.000Z",
    "updatedAt": "2025-07-29T17:07:15.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Dolorem aut nostrud",
    "lastName": "Ad in qui elit nost",
    "dob": "1988-06-19T00:00:00.000Z",
    "ssn": "Ullamco laboris amet",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1760-126594",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "männlich",
    "addresses": [
      {
        "id": 1772,
        "label": null,
        "name": null,
        "street": "Aut quas et ut repud",
        "streetNumber": "Sint incidunt molli",
        "postalCode": "Culpa",
        "city": "Aliquam molestias in",
        "country": "GR",
        "primary": 1
      }
    ],
    "categories": [
      13
    ],
    "customFields": [
      11547,
      11548,
      11549,
      11550,
      11551,
      11552
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1774.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasCreated",
  "model": "Patient",
  "id": 1774,
  "payload": {
    "id": 1774,
    "createdAt": "2025-07-29T17:07:15.000Z",
    "updatedAt": "2025-07-29T17:07:15.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Dolorem aut nostrud",
    "lastName": "Ad in qui elit nost",
    "dob": "1988-06-19T00:00:00.000Z",
    "ssn": "Ullamco laboris amet",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1760-126594",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "männlich",
    "addresses": [
      {
        "id": 1772,
        "label": null,
        "name": null,
        "street": "Aut quas et ut repud",
        "streetNumber": "Sint incidunt molli",
        "postalCode": "Culpa",
        "city": "Aliquam molestias in",
        "country": "GR",
        "primary": 1
      }
    ],
    "categories": [
      13
    ],
    "customFields": [
      11547,
      11548,
      11549,
      11550,
      11551,
      11552
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1774.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-29T17:07:16.499Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasCreated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
