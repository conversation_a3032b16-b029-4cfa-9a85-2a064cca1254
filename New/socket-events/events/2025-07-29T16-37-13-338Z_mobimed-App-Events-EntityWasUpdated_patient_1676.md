# mobimed:App\Events\EntityWasUpdated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasUpdated
- **Model**: patient
- **Event ID**: 1676
- **Timestamp**: 2025-07-29T16:37:13.338Z
- **Webhook Event**: EntityWasUpdated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1676,
    "createdAt": "2025-01-13T20:38:40.000Z",
    "updatedAt": "2025-07-29T16:37:10.000Z",
    "createdBy": 5016,
    "updatedBy": 5003,
    "firstName": "test",
    "lastName": "7861",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": null,
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": null,
    "title": "Title test",
    "titleSuffix": "Titel (nachgestellt)",
    "healthInsurance": null,
    "gender": null,
    "addresses": [
      {
        "id": 1771,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      11540,
      11541
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [
      14428
    ],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1676.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasUpdated",
  "model": "Patient",
  "id": 1676,
  "payload": {
    "id": 1676,
    "createdAt": "2025-01-13T20:38:40.000Z",
    "updatedAt": "2025-07-29T16:37:10.000Z",
    "createdBy": 5016,
    "updatedBy": 5003,
    "firstName": "test",
    "lastName": "7861",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": null,
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": null,
    "title": "Title test",
    "titleSuffix": "Titel (nachgestellt)",
    "healthInsurance": null,
    "gender": null,
    "addresses": [
      {
        "id": 1771,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      11540,
      11541
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [
      14428
    ],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1676.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-29T16:37:13.338Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasUpdated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
